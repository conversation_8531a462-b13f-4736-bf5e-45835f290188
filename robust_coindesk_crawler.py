#!/usr/bin/env python3
"""
Robust CoinDesk Data API Documentation Crawler
Handles JavaScript-rendered content and protections
"""

import asyncio
from crawl4ai import AsyncWebCrawler
import os
import json
from datetime import datetime
from urllib.parse import urljoin, urlparse
import re
import time

class RobustCoinDeskCrawler:
    def __init__(self):
        self.base_url = "https://developers.coindesk.com"
        self.crawled_urls = set()
        self.failed_urls = set()
        self.output_dir = f"coindesk_api_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.delay = 2  # Longer delay to be more respectful
        
        # Target API sections
        self.target_sections = [
            "indices",
            "ref-rates", 
            "reference-rates",
            "spot",
            "futures",
            "news",
            "overview"
        ]
        
        # Priority pages to crawl with variations
        self.priority_pages = [
            "/documentation/data-api/introduction",
            "/documentation/data-api/overview", 
            "/documentation/data-api/getting-started",
            "/documentation/data-api/authentication",
            "/documentation/data-api/rate-limits",
            "/documentation/data-api/errors",
            "/documentation/data-api/endpoints",
            "/documentation/data-api/indices",
            "/documentation/data-api/reference-rates",
            "/documentation/data-api/spot",
            "/documentation/data-api/futures",
            "/documentation/data-api/news",
            "/documentation/data-api",
            "/documentation/data-api/",
            "/docs/data-api/introduction",
            "/docs/data-api/overview",
            "/docs/data-api/indices",
            "/docs/data-api/reference-rates", 
            "/docs/data-api/spot",
            "/docs/data-api/futures",
            "/docs/data-api/news"
        ]
        
    async def setup_output_dir(self):
        """Create output directory"""
        os.makedirs(self.output_dir, exist_ok=True)
        
    def extract_title_from_markdown(self, markdown_content):
        """Extract title from markdown content"""
        lines = markdown_content.split('\n')
        for line in lines:
            if line.startswith('# '):
                return line[2:].strip()
        return "CoinDesk API Documentation"
    
    def is_valid_url(self, url):
        """Check if URL should be crawled"""
        if not url.startswith(self.base_url):
            return False
        
        # Skip certain file types
        skip_extensions = ['.pdf', '.jpg', '.png', '.gif', '.css', '.js', '.ico', '.svg']
        if any(url.lower().endswith(ext) for ext in skip_extensions):
            return False
            
        # Skip login and edit pages
        skip_patterns = ['/login', '/edit/', '/logout', '/signin', '/signup', '#']
        if any(pattern in url for pattern in skip_patterns):
            return False
            
        return True
    
    def is_target_api_page(self, url):
        """Check if URL is related to our target API sections"""
        url_lower = url.lower()
        
        # Always include documentation/data-api pages
        if '/documentation/data-api' in url_lower or '/docs/data-api' in url_lower:
            return True
            
        # Check for target sections
        for section in self.target_sections:
            if section in url_lower:
                return True
                
        return False
    
    async def crawl_page_with_js(self, crawler, url):
        """Crawl a single page with JavaScript support"""
        if url in self.crawled_urls:
            return None
            
        print(f"🕷️  Crawling (JS-enabled): {url}")
        
        try:
            # Try with JavaScript execution and longer wait times
            result = await crawler.arun(
                url=url,
                bypass_cache=True,
                word_count_threshold=10,
                wait_for_images=False,
                js_code=[
                    "window.scrollTo(0, document.body.scrollHeight);",
                    "await new Promise(resolve => setTimeout(resolve, 2000));",  # Wait 2 seconds
                    "window.scrollTo(0, 0);"
                ],
                css_selector="body",
                exclude_external_links=True,
                exclude_social_media_links=True
            )
            
            if result.success and len(result.markdown.strip()) > 100:
                self.crawled_urls.add(url)
                
                # Extract title from content
                title = self.extract_title_from_markdown(result.markdown)
                
                # Create filename from URL path
                path = urlparse(url).path
                if path == "/" or path == "":
                    filename = "index.md"
                else:
                    filename = path.strip('/').replace('/', '_').replace('-', '_') + '.md'
                
                filepath = os.path.join(self.output_dir, filename)
                
                # Save content
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(f"# {title}\n\n")
                    f.write(f"**URL:** {url}\n")
                    f.write(f"**Crawled:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    f.write("---\n\n")
                    f.write(result.markdown)
                
                print(f"✅ Saved: {filename} ({len(result.markdown)} chars)")
                
                return {
                    'url': url,
                    'filename': filename,
                    'content_length': len(result.markdown),
                    'title': title,
                    'success': True
                }
            else:
                print(f"❌ Failed or empty content: {url}")
                self.failed_urls.add(url)
                return None
                
        except Exception as e:
            print(f"❌ Error crawling {url}: {str(e)}")
            self.failed_urls.add(url)
            return None
    
    async def crawl_specific_endpoints(self, crawler):
        """Try to crawl specific known endpoints directly"""
        specific_endpoints = [
            f"{self.base_url}/documentation/data-api/introduction",
            f"{self.base_url}/documentation/data-api/overview",
            f"{self.base_url}/documentation/data-api/indices",
            f"{self.base_url}/documentation/data-api/reference-rates",
            f"{self.base_url}/documentation/data-api/spot",
            f"{self.base_url}/documentation/data-api/futures",
            f"{self.base_url}/documentation/data-api/news",
            f"{self.base_url}/documentation/data-api/authentication"
        ]
        
        results = []
        for url in specific_endpoints:
            if url not in self.crawled_urls:
                result = await self.crawl_page_with_js(crawler, url)
                if result:
                    results.append(result)
                await asyncio.sleep(self.delay)
        
        return results
    
    async def crawl_all(self):
        """Main crawling function with robust handling"""
        await self.setup_output_dir()
        
        # Configure crawler with more robust settings
        crawler_config = {
            'verbose': True,
            'headless': True,
            'browser_type': 'chromium',
            'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
        
        async with AsyncWebCrawler(**crawler_config) as crawler:
            print(f"🚀 Starting robust CoinDesk Data API crawl")
            print(f"🎯 Target sections: {', '.join(self.target_sections)}")
            print(f"📁 Output directory: {self.output_dir}")
            print("=" * 60)
            
            crawl_results = []
            
            # First, try the main documentation page
            main_url = f"{self.base_url}/documentation/data-api"
            print(f"🏠 Starting with main page: {main_url}")
            
            main_result = await self.crawl_page_with_js(crawler, main_url)
            if main_result:
                crawl_results.append(main_result)
            
            await asyncio.sleep(self.delay)
            
            # Try specific endpoints we know about
            print(f"🎯 Crawling specific API endpoints...")
            specific_results = await self.crawl_specific_endpoints(crawler)
            crawl_results.extend(specific_results)
            
            # Try priority pages
            print(f"📋 Crawling priority pages...")
            for path in self.priority_pages:
                url = urljoin(self.base_url, path)
                if url not in self.crawled_urls:
                    result = await self.crawl_page_with_js(crawler, url)
                    if result:
                        crawl_results.append(result)
                    await asyncio.sleep(self.delay)
            
            # Save crawl summary
            summary = {
                'crawl_date': datetime.now().isoformat(),
                'target_sections': self.target_sections,
                'total_pages_crawled': len(crawl_results),
                'failed_urls': list(self.failed_urls),
                'crawled_pages': crawl_results
            }
            
            summary_file = os.path.join(self.output_dir, 'crawl_summary.json')
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2)
            
            # Create index file
            await self.create_index_file(crawl_results)
            
            print("\n" + "=" * 60)
            print(f"🎉 CoinDesk API Crawl Complete!")
            print(f"📊 Pages crawled: {len(crawl_results)}")
            print(f"❌ Failed URLs: {len(self.failed_urls)}")
            print(f"📁 Output directory: {self.output_dir}")
            
            if self.failed_urls:
                print(f"\n⚠️  Failed URLs:")
                for url in list(self.failed_urls)[:10]:
                    print(f"   • {url}")
            
            # Show what we successfully crawled
            if crawl_results:
                print(f"\n✅ Successfully crawled:")
                for result in crawl_results:
                    print(f"   • {result['title']} ({result['content_length']} chars)")
    
    async def create_index_file(self, crawl_results):
        """Create an index file with all crawled content"""
        index_file = os.path.join(self.output_dir, 'README.md')
        
        with open(index_file, 'w', encoding='utf-8') as f:
            f.write("# CoinDesk Data API Documentation - Complete Crawl\n\n")
            f.write(f"**Crawled on:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**Target sections:** {', '.join(self.target_sections)}\n")
            f.write(f"**Total pages:** {len(crawl_results)}\n\n")
            f.write("## 📋 Crawled API Documentation\n\n")
            
            # Group by API section
            indices_pages = [r for r in crawl_results if any(s in r['url'].lower() for s in ['indices', 'ref-rates', 'reference-rates'])]
            spot_pages = [r for r in crawl_results if 'spot' in r['url'].lower()]
            futures_pages = [r for r in crawl_results if 'futures' in r['url'].lower()]
            news_pages = [r for r in crawl_results if 'news' in r['url'].lower()]
            overview_pages = [r for r in crawl_results if 'overview' in r['url'].lower() or 'introduction' in r['url'].lower()]
            other_pages = [r for r in crawl_results if r not in indices_pages + spot_pages + futures_pages + news_pages + overview_pages]
            
            if overview_pages:
                f.write("### 🔍 Overview & Introduction\n\n")
                for result in sorted(overview_pages, key=lambda x: x['url']):
                    f.write(f"- [{result['title']}]({result['filename']}) - {result['content_length']} chars\n")
                f.write("\n")
            
            if indices_pages:
                f.write("### 📊 Indices & Reference Rates\n\n")
                for result in sorted(indices_pages, key=lambda x: x['url']):
                    f.write(f"- [{result['title']}]({result['filename']}) - {result['content_length']} chars\n")
                f.write("\n")
            
            if spot_pages:
                f.write("### 💰 Spot Data\n\n")
                for result in sorted(spot_pages, key=lambda x: x['url']):
                    f.write(f"- [{result['title']}]({result['filename']}) - {result['content_length']} chars\n")
                f.write("\n")
            
            if futures_pages:
                f.write("### 📈 Futures Data\n\n")
                for result in sorted(futures_pages, key=lambda x: x['url']):
                    f.write(f"- [{result['title']}]({result['filename']}) - {result['content_length']} chars\n")
                f.write("\n")
            
            if news_pages:
                f.write("### 📰 News API\n\n")
                for result in sorted(news_pages, key=lambda x: x['url']):
                    f.write(f"- [{result['title']}]({result['filename']}) - {result['content_length']} chars\n")
                f.write("\n")
            
            if other_pages:
                f.write("### 📚 Other Documentation\n\n")
                for result in sorted(other_pages, key=lambda x: x['url']):
                    f.write(f"- [{result['title']}]({result['filename']}) - {result['content_length']} chars\n")

if __name__ == "__main__":
    crawler = RobustCoinDeskCrawler()
    asyncio.run(crawler.crawl_all())
