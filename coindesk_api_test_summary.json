{"test_date": "2025-05-30T12:11:49.647829", "total_endpoints": 4, "successful": 0, "failed": 4, "results": [{"endpoint": {"name": "Bitcoin Price Index - Current", "url": "https://api.coindesk.com/v1/bpi/currentprice.json", "description": "Current Bitcoin price in multiple currencies"}, "status": "error", "error": "HTTPSConnectionPool(host='api.coindesk.com', port=443): Max retries exceeded with url: /v1/bpi/currentprice.json (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x105fafd50>: Failed to resolve 'api.coindesk.com' ([Errno 8] nodename nor servname provided, or not known)\"))"}, {"endpoint": {"name": "Bitcoin Price Index - Historical", "url": "https://api.coindesk.com/v1/bpi/historical/close.json", "description": "Historical Bitcoin price data"}, "status": "error", "error": "HTTPSConnectionPool(host='api.coindesk.com', port=443): Max retries exceeded with url: /v1/bpi/historical/close.json (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x105fd1bd0>: Failed to resolve 'api.coindesk.com' ([Errno 8] nodename nor servname provided, or not known)\"))"}, {"endpoint": {"name": "BPI - USD Only", "url": "https://api.coindesk.com/v1/bpi/currentprice/USD.json", "description": "Current Bitcoin price in USD only"}, "status": "error", "error": "HTTPSConnectionPool(host='api.coindesk.com', port=443): Max retries exceeded with url: /v1/bpi/currentprice/USD.json (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x105fd3e10>: Failed to resolve 'api.coindesk.com' ([Errno 8] nodename nor servname provided, or not known)\"))"}, {"endpoint": {"name": "BPI - EUR Only", "url": "https://api.coindesk.com/v1/bpi/currentprice/EUR.json", "description": "Current Bitcoin price in EUR only"}, "status": "error", "error": "HTTPSConnectionPool(host='api.coindesk.com', port=443): Max retries exceeded with url: /v1/bpi/currentprice/EUR.json (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x105faff10>: Failed to resolve 'api.coindesk.com' ([Errno 8] nodename nor servname provided, or not known)\"))"}]}