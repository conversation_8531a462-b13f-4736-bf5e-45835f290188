#!/usr/bin/env python3
"""
Test CoinDesk API endpoints to gather information
"""

import requests
import json
from datetime import datetime

def test_coindesk_endpoints():
    """Test known CoinDesk API endpoints"""
    
    print("🧪 Testing CoinDesk API Endpoints")
    print("=" * 50)
    
    # Known working endpoints
    endpoints = [
        {
            "name": "Bitcoin Price Index - Current",
            "url": "https://api.coindesk.com/v1/bpi/currentprice.json",
            "description": "Current Bitcoin price in multiple currencies"
        },
        {
            "name": "Bitcoin Price Index - Historical",
            "url": "https://api.coindesk.com/v1/bpi/historical/close.json",
            "description": "Historical Bitcoin price data"
        },
        {
            "name": "BPI - USD Only",
            "url": "https://api.coindesk.com/v1/bpi/currentprice/USD.json",
            "description": "Current Bitcoin price in USD only"
        },
        {
            "name": "BPI - EUR Only", 
            "url": "https://api.coindesk.com/v1/bpi/currentprice/EUR.json",
            "description": "Current Bitcoin price in EUR only"
        }
    ]
    
    results = []
    
    for endpoint in endpoints:
        print(f"\n🔍 Testing: {endpoint['name']}")
        print(f"📍 URL: {endpoint['url']}")
        
        try:
            response = requests.get(endpoint['url'], timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Success! Status: {response.status_code}")
                print(f"📊 Response size: {len(response.text)} characters")
                
                # Save the response
                filename = f"coindesk_{endpoint['name'].lower().replace(' ', '_').replace('-', '_')}.json"
                with open(filename, 'w') as f:
                    json.dump(data, f, indent=2)
                
                print(f"💾 Saved to: {filename}")
                
                # Show a preview of the data
                if 'bpi' in data:
                    print("📋 Preview:")
                    if 'USD' in data['bpi']:
                        print(f"   💰 USD Rate: {data['bpi']['USD'].get('rate', 'N/A')}")
                    if 'time' in data:
                        print(f"   🕐 Updated: {data['time'].get('updated', 'N/A')}")
                
                results.append({
                    "endpoint": endpoint,
                    "status": "success",
                    "status_code": response.status_code,
                    "data_size": len(response.text),
                    "filename": filename
                })
                
            else:
                print(f"❌ Failed! Status: {response.status_code}")
                results.append({
                    "endpoint": endpoint,
                    "status": "failed",
                    "status_code": response.status_code,
                    "error": f"HTTP {response.status_code}"
                })
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            results.append({
                "endpoint": endpoint,
                "status": "error",
                "error": str(e)
            })
    
    # Create summary
    print(f"\n" + "=" * 50)
    print(f"📊 Summary:")
    successful = [r for r in results if r['status'] == 'success']
    failed = [r for r in results if r['status'] != 'success']
    
    print(f"✅ Successful: {len(successful)}")
    print(f"❌ Failed: {len(failed)}")
    
    if successful:
        print(f"\n🎉 Working endpoints found!")
        for result in successful:
            print(f"   • {result['endpoint']['name']}")
    
    # Save summary
    summary = {
        "test_date": datetime.now().isoformat(),
        "total_endpoints": len(endpoints),
        "successful": len(successful),
        "failed": len(failed),
        "results": results
    }
    
    with open("coindesk_api_test_summary.json", 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"\n💾 Test summary saved to: coindesk_api_test_summary.json")
    
    return results

if __name__ == "__main__":
    test_coindesk_endpoints()
