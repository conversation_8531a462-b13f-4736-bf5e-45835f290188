{"crawl_date": "2025-05-30T12:10:43.833355", "target_sections": ["indices", "ref-rates", "reference-rates", "spot", "futures", "news", "overview"], "total_pages_crawled": 0, "failed_urls": ["https://developers.coindesk.com/documentation/data-api/errors", "https://developers.coindesk.com/documentation/data-api/indices", "https://developers.coindesk.com/docs/data-api/indices", "https://developers.coindesk.com/documentation/data-api", "https://developers.coindesk.com/documentation/data-api/endpoints", "https://developers.coindesk.com/docs/data-api/spot", "https://developers.coindesk.com/docs/data-api/futures", "https://developers.coindesk.com/documentation/data-api/futures", "https://developers.coindesk.com/docs/data-api/introduction", "https://developers.coindesk.com/docs/data-api/overview", "https://developers.coindesk.com/docs/data-api/reference-rates", "https://developers.coindesk.com/documentation/data-api/spot", "https://developers.coindesk.com/documentation/data-api/overview", "https://developers.coindesk.com/documentation/data-api/", "https://developers.coindesk.com/documentation/data-api/getting-started", "https://developers.coindesk.com/documentation/data-api/introduction", "https://developers.coindesk.com/documentation/data-api/rate-limits", "https://developers.coindesk.com/documentation/data-api/news", "https://developers.coindesk.com/documentation/data-api/reference-rates", "https://developers.coindesk.com/documentation/data-api/authentication", "https://developers.coindesk.com/docs/data-api/news"], "crawled_pages": []}