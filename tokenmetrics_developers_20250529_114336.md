# TokenMetrics Developers Documentation

**URL:** https://developers.tokenmetrics.com/
**Crawled:** 2025-05-29 11:43:36

---

[Jump to Content](https://developers.tokenmetrics.com/docs/getting-started#content)
[![Token Metrics - API Guides and Documentation](https://files.readme.io/6141d8ec9ddb9dd233e52357e7526ba5fea3dacafab20cd042bc20a2de070beb-dark_mode_1.svg)](https://developers.tokenmetrics.com/)
[Guides](https://developers.tokenmetrics.com/docs)[API Reference](https://developers.tokenmetrics.com/reference)v2[Log In](https://developers.tokenmetrics.com/login?redirect_uri=/docs/getting-started)[![Token Metrics - API Guides and Documentation](https://files.readme.io/6141d8ec9ddb9dd233e52357e7526ba5fea3dacafab20cd042bc20a2de070beb-dark_mode_1.svg)](https://developers.tokenmetrics.com/)
Guides
[Log In](https://developers.tokenmetrics.com/login?redirect_uri=/docs/getting-started)
Moon (Dark Mode)Sun (Light Mode)
Token Metrics API
[Guides](https://developers.tokenmetrics.com/docs)[API Reference](https://developers.tokenmetrics.com/reference)📘 Introduction
Search
## Token Metrics API Documentation
  * [📘 Introduction](https://developers.tokenmetrics.com/docs/getting-started)
    * [🚀 Getting Started with the Token Metrics API](https://developers.tokenmetrics.com/docs/getting-started-with-the-token-metrics-api)
    * [👨🏻‍💻 Monitoring API Keys and Usage](https://developers.tokenmetrics.com/docs/tokenmetrics-api)
    * [🔑 Setting Up Your API Key](https://developers.tokenmetrics.com/docs/setting-up-your-api-key)
    * [🔓 Authentication](https://developers.tokenmetrics.com/docs/authentication)
    * [❌ Common Errors & Rate Limit](https://developers.tokenmetrics.com/docs/common-errors-rate-limit)
    * [🔗 Useful Links](https://developers.tokenmetrics.com/docs/useful-links)
  * [📍Endpoints](https://developers.tokenmetrics.com/docs/endpoints)
    * [Tokens](https://developers.tokenmetrics.com/docs/tokens-guide)
    * [Trader Grades](https://developers.tokenmetrics.com/docs/trader-grades-guide)
    * [Hourly OHLCV](https://developers.tokenmetrics.com/docs/hourly-ohlcv-guide)
    * [Daily OHLCV](https://developers.tokenmetrics.com/docs/daily-ohlcv-guide)
    * [Investor Grades](https://developers.tokenmetrics.com/docs/investor-grades-guide)
    * [Market Metrics](https://developers.tokenmetrics.com/docs/market-metrics-guide)
    * [Trading Signals](https://developers.tokenmetrics.com/docs/trading-signals-guide)
    * [Resistance & Support](https://developers.tokenmetrics.com/docs/resistance-support-guide)
    * [Price](https://developers.tokenmetrics.com/docs/price-guide)
    * [Token Metrics AI Agent](https://developers.tokenmetrics.com/docs/tm-ai)
    * [Sentiment](https://developers.tokenmetrics.com/docs/sentiment-guide)
    * [Quantmetrics](https://developers.tokenmetrics.com/docs/quantmetrics-guide)
    * [Scenario Analysis](https://developers.tokenmetrics.com/docs/scenario-analysis-guide)
    * [Correlation](https://developers.tokenmetrics.com/docs/correlation-guide)
    * [AI Reports](https://developers.tokenmetrics.com/docs/ai-reports-guide)
    * [Crypto Investors](https://developers.tokenmetrics.com/docs/crypto-investors-guide)
    * [Top Tokens by Market Cap](https://developers.tokenmetrics.com/docs/tokens-top-market-cap)
    * [All Trend Indicators](https://developers.tokenmetrics.com/docs/all-trend-indicators-guide)
    * [Sector Indices Holdings](https://developers.tokenmetrics.com/docs/sector-indices-holdings-guide)
    * [Sector Indices Performance](https://developers.tokenmetrics.com/docs/sector-indices-performance)
    * [Sector Index Transaction](https://developers.tokenmetrics.com/docs/sector-index-transaction-guide)
  * [📄 Terms & Conditions](https://developers.tokenmetrics.com/docs/terms-and-conditions)
    * [1. Disclaimer](https://developers.tokenmetrics.com/docs/12-disclaimer)
    * [2. Defined Terms](https://developers.tokenmetrics.com/docs/1-defined-terms)
    * [3. Scope and Application Registration](https://developers.tokenmetrics.com/docs/2-scope-and-application-registration)
    * [4. License and Guidelines](https://developers.tokenmetrics.com/docs/3-token-metrics-apis-license-and-guidelines)
    * [5. Security](https://developers.tokenmetrics.com/docs/4-security)
    * [6. Your Compliance with Applicable Privacy and Data Protection Laws](https://developers.tokenmetrics.com/docs/5-your-compliance-with-applicable-privacy-and-data-protection-laws)
    * [7. Changes to the Token Metrics APIs and API Terms](https://developers.tokenmetrics.com/docs/6-changes-to-the-token-metrics-apis-and-api-terms)
    * [8. Feedback](https://developers.tokenmetrics.com/docs/7-feedback)
    * [9. Confidentiality](https://developers.tokenmetrics.com/docs/8-confidentiality)
    * [10. Disclaimer of Warranties, Limitation of Liability and Indemnity](https://developers.tokenmetrics.com/docs/9-disclaimer-of-warranties-limitation-of-liability-and-indemnity)
    * [11. Termination](https://developers.tokenmetrics.com/docs/10-termination)
    * [12. General Terms](https://developers.tokenmetrics.com/docs/11-general-terms)
  * [🧰 Resources](https://developers.tokenmetrics.com/docs/resources)
    * [Token Metrics API Pricing](https://www.tokenmetrics.com/crypto-data-api)
    * [FAQ](https://www.tokenmetrics.com/faq)
    * [Contact Us](https://www.tokenmetrics.com/contact-us)
    * [E-book](https://www.tokenmetrics.com/crypto-investing-guide)
    * [Blog](https://www.tokenmetrics.com/blog)


## TMAI WHitepaper
  * [Introduction](https://developers.tokenmetrics.com/docs/introduction-1)
  * [Token Metrics Roadmap](https://developers.tokenmetrics.com/docs/token-metrics-roadmap)
  * [Token Economy & Gamification](https://developers.tokenmetrics.com/docs/token-economy-gamification)
    * [Stake to Access](https://developers.tokenmetrics.com/docs/staking-score-staking-vaults-and-lockups)
    * [Token Metrics DAO](https://developers.tokenmetrics.com/docs/token-metrics-dao)
    * [Trading Platform Discounts](https://developers.tokenmetrics.com/docs/trading-bot-discounts)
  * [$TMAI Token](https://developers.tokenmetrics.com/docs/tmai-1)
    * [Allocation And Sale](https://developers.tokenmetrics.com/docs/allocation-and-sale-1)
    * [Release Schedule](https://developers.tokenmetrics.com/docs/release)
    * [Team](https://developers.tokenmetrics.com/docs/team)


Powered by [](https://readme.com?ref_src=hub&project=tm-api)
# 📘 Introduction
[ Suggest Edits](https://developers.tokenmetrics.com/edit/getting-started)
Founded in **2019** , **Token Metrics** set out to give every trader and investor the data edge once reserved for quant desks. Today more than **70,000 users** rely on our AI-powered research platform—and the **Token Metrics API** exposes that same edge through production-ready API endpoints.
Get real-time and historical prices, proprietary Trader & Investor Grades, smart indices, deep-dive AI reports, sentiment metrics, and even a conversational crypto agent you can query in plain English. Whether you’re automating trades, building dashboards, or crunching on-chain research, the API delivers institutional-grade data in a single, unified service.
## 
🔑 Key Features
[](https://developers.tokenmetrics.com/docs/getting-started#-key-features)
  * **Real-Time & Historical Market Data** Access price, volume, and liquidity snapshots for hundreds of crypto assets—both live and backfilled.
  * **AI-Powered Grading Systems** Instantly evaluate coins with proprietary _Trader_ and _Investor_ Grades.
  * **Comprehensive Technical Indicators** Query support/resistance levels, correlations, volatility metrics, and more.
  * **Deep-Dive AI Reports** Pull auto-generated research briefs that summarize fundamentals and on-chain data.
  * **Conversational Crypto Agent** Ask questions in plain English and receive token facts or instant dashboards on demand.


## 
🛠️ Use Cases
[](https://developers.tokenmetrics.com/docs/getting-started#️-use-cases)
  * **Automated Trading Agents** : Develop bots that execute trades based on AI-generated signals.
  * **Market Analysis Dashboards** : Build platforms that visualize market trends and insights.
  * **Portfolio Optimization Tools** : Create applications that help users optimize their crypto holdings.
  * **Research & Reporting**: Generate in-depth reports using comprehensive market data and analytics.


## 
📞 Support
[](https://developers.tokenmetrics.com/docs/getting-started#-support)
For assistance or inquiries:
  * **Email** : <EMAIL>


_Empower your crypto strategies with the intelligence of the Token Metrics API._
Updated 7 days ago 
Ask AI
  * [Table of Contents](https://developers.tokenmetrics.com/docs/getting-started)
  *     * [🔑 Key Features](https://developers.tokenmetrics.com/docs/getting-started#-key-features)
    * [🛠️ Use Cases](https://developers.tokenmetrics.com/docs/getting-started#%EF%B8%8F-use-cases)
    * [📞 Support](https://developers.tokenmetrics.com/docs/getting-started#-support)


