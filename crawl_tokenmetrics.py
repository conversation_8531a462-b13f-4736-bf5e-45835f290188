#!/usr/bin/env python3
"""
Crawl TokenMetrics developers website and save as markdown
"""

import asyncio
from crawl4ai import AsyncWebCrawler
import os
from datetime import datetime

async def crawl_tokenmetrics():
    """Crawl the TokenMetrics developers website and save as markdown"""

    url = "https://developers.tokenmetrics.com/"

    # Create output directory if it doesn't exist
    output_dir = "tokenmetrics_crawl"
    os.makedirs(output_dir, exist_ok=True)

    async with AsyncWebCrawler(verbose=True) as crawler:
        print(f"🚀 Starting to crawl: {url}")

        # Crawl the main page
        result = await crawler.arun(
            url=url,
            word_count_threshold=10,
            bypass_cache=True,
            include_raw_html=True
        )

        if result.success:
            print("✅ Successfully crawled the website!")

            # Generate timestamp for filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Save main content as markdown
            main_filename = f"{output_dir}/tokenmetrics_developers_{timestamp}.md"
            with open(main_filename, 'w', encoding='utf-8') as f:
                f.write(f"# TokenMetrics Developers Documentation\n\n")
                f.write(f"**URL:** {url}\n")
                f.write(f"**Crawled on:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write("---\n\n")
                f.write(result.markdown)

            print(f"📄 Main content saved to: {main_filename}")

            # Save extracted content if available
            if hasattr(result, 'extracted_content') and result.extracted_content:
                extracted_filename = f"{output_dir}/tokenmetrics_extracted_{timestamp}.md"
                with open(extracted_filename, 'w', encoding='utf-8') as f:
                    f.write(f"# TokenMetrics Developers - Extracted Content\n\n")
                    f.write(f"**URL:** {url}\n")
                    f.write(f"**Extracted on:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    f.write("---\n\n")
                    f.write(str(result.extracted_content))

                print(f"🎯 Extracted content saved to: {extracted_filename}")

            # Save raw HTML for reference
            html_filename = f"{output_dir}/tokenmetrics_raw_{timestamp}.html"
            with open(html_filename, 'w', encoding='utf-8') as f:
                f.write(result.html)

            print(f"🔧 Raw HTML saved to: {html_filename}")

            # Print summary
            print(f"\n📊 Crawl Summary:")
            print(f"   • URL: {result.url}")
            print(f"   • Status: {'✅ Success' if result.success else '❌ Failed'}")
            print(f"   • Content length: {len(result.markdown)} characters")
            print(f"   • Links found: {len(result.links)}")
            print(f"   • Images found: {len(result.media.get('images', []))}")

            # Save links for potential future crawling
            if result.links:
                links_filename = f"{output_dir}/tokenmetrics_links_{timestamp}.md"
                with open(links_filename, 'w', encoding='utf-8') as f:
                    f.write(f"# Links Found on TokenMetrics Developers\n\n")
                    f.write(f"**Total links:** {len(result.links)}\n\n")
                    for i, link in enumerate(result.links, 1):
                        if isinstance(link, dict):
                            f.write(f"{i}. [{link.get('text', 'No text')}]({link.get('href', '#')})\n")
                        else:
                            f.write(f"{i}. {link}\n")

                print(f"🔗 Links saved to: {links_filename}")

        else:
            print(f"❌ Failed to crawl {url}")
            print(f"Error: {result.error_message}")

if __name__ == "__main__":
    print("🕷️  TokenMetrics Developer Documentation Crawler")
    print("=" * 50)
    asyncio.run(crawl_tokenmetrics())
