#!/usr/bin/env python3
"""
Simple crawl of TokenMetrics developers website
"""

import asyncio
from crawl4ai import Async<PERSON>ebCrawler
import os
from datetime import datetime

async def simple_crawl():
    """Simple crawl and save as markdown"""
    
    url = "https://developers.tokenmetrics.com/"
    
    async with AsyncWebCrawler(verbose=True) as crawler:
        print(f"🚀 Crawling: {url}")
        
        result = await crawler.arun(
            url=url,
            bypass_cache=True
        )
        
        if result.success:
            print("✅ Success!")
            
            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"tokenmetrics_developers_{timestamp}.md"
            
            # Save markdown content
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"# TokenMetrics Developers Documentation\n\n")
                f.write(f"**URL:** {url}\n")
                f.write(f"**Crawled:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write("---\n\n")
                f.write(result.markdown)
            
            print(f"📄 Saved to: {filename}")
            print(f"📊 Content length: {len(result.markdown)} characters")
            
        else:
            print(f"❌ Failed: {result.error_message}")

if __name__ == "__main__":
    asyncio.run(simple_crawl())
