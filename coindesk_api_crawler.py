#!/usr/bin/env python3
"""
CoinDesk Data API Documentation Crawler
Focuses on: Indices & Ref. Rates, Spot, Futures, News, Overview
"""

import asyncio
from crawl4ai import AsyncWebCrawler
import os
import json
from datetime import datetime
from urllib.parse import urljoin, urlparse
import re

class CoinDeskAPICrawler:
    def __init__(self):
        self.base_url = "https://developers.coindesk.com"
        self.crawled_urls = set()
        self.failed_urls = set()
        self.output_dir = f"coindesk_api_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.delay = 1
        
        # Target API sections
        self.target_sections = [
            "indices",
            "ref-rates", 
            "reference-rates",
            "spot",
            "futures",
            "news",
            "overview"
        ]
        
        # Priority pages to crawl
        self.priority_pages = [
            "/documentation/data-api/introduction",
            "/documentation/data-api/overview",
            "/documentation/data-api/indices",
            "/documentation/data-api/reference-rates",
            "/documentation/data-api/spot",
            "/documentation/data-api/futures", 
            "/documentation/data-api/news",
            "/documentation/data-api/authentication",
            "/documentation/data-api/rate-limits",
            "/documentation/data-api/errors",
            "/documentation/data-api/getting-started",
            "/documentation/data-api/endpoints"
        ]
        
    async def setup_output_dir(self):
        """Create output directory"""
        os.makedirs(self.output_dir, exist_ok=True)
        
    def extract_title_from_markdown(self, markdown_content):
        """Extract title from markdown content"""
        lines = markdown_content.split('\n')
        for line in lines:
            if line.startswith('# '):
                return line[2:].strip()
        return "CoinDesk API Documentation"
    
    def is_valid_url(self, url):
        """Check if URL should be crawled"""
        if not url.startswith(self.base_url):
            return False
        
        # Skip certain file types
        skip_extensions = ['.pdf', '.jpg', '.png', '.gif', '.css', '.js', '.ico']
        if any(url.lower().endswith(ext) for ext in skip_extensions):
            return False
            
        # Skip login and edit pages
        skip_patterns = ['/login', '/edit/', '/logout', '#']
        if any(pattern in url for pattern in skip_patterns):
            return False
            
        return True
    
    def is_target_api_page(self, url):
        """Check if URL is related to our target API sections"""
        url_lower = url.lower()
        
        # Always include documentation/data-api pages
        if '/documentation/data-api/' in url_lower:
            return True
            
        # Check for target sections
        for section in self.target_sections:
            if section in url_lower:
                return True
                
        return False
    
    def extract_links_from_content(self, content, base_url):
        """Extract links from markdown content"""
        markdown_links = re.findall(r'\[([^\]]*)\]\(([^)]+)\)', content)
        links = []
        
        for text, url in markdown_links:
            if url.startswith('http'):
                full_url = url
            else:
                full_url = urljoin(base_url, url)
            
            if self.is_valid_url(full_url) and self.is_target_api_page(full_url):
                links.append(full_url)
                
        return list(set(links))
    
    async def crawl_page(self, crawler, url):
        """Crawl a single page"""
        if url in self.crawled_urls:
            return None
            
        print(f"🕷️  Crawling: {url}")
        
        try:
            result = await crawler.arun(
                url=url,
                bypass_cache=True,
                word_count_threshold=10
            )
            
            if result.success:
                self.crawled_urls.add(url)
                
                # Extract title from content
                title = self.extract_title_from_markdown(result.markdown)
                
                # Create filename from URL path
                path = urlparse(url).path
                if path == "/" or path == "":
                    filename = "index.md"
                else:
                    filename = path.strip('/').replace('/', '_') + '.md'
                
                filepath = os.path.join(self.output_dir, filename)
                
                # Save content
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(f"# {title}\n\n")
                    f.write(f"**URL:** {url}\n")
                    f.write(f"**Crawled:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    f.write("---\n\n")
                    f.write(result.markdown)
                
                print(f"✅ Saved: {filename} ({len(result.markdown)} chars)")
                
                # Extract links for further crawling
                links = self.extract_links_from_content(result.markdown, url)
                
                return {
                    'url': url,
                    'filename': filename,
                    'content_length': len(result.markdown),
                    'links': links,
                    'title': title
                }
            else:
                print(f"❌ Failed: {url} - {result.error_message}")
                self.failed_urls.add(url)
                return None
                
        except Exception as e:
            print(f"❌ Error crawling {url}: {str(e)}")
            self.failed_urls.add(url)
            return None
    
    async def crawl_all(self):
        """Main crawling function"""
        await self.setup_output_dir()
        
        async with AsyncWebCrawler(verbose=False) as crawler:
            print(f"🚀 Starting CoinDesk Data API crawl")
            print(f"🎯 Target sections: {', '.join(self.target_sections)}")
            print(f"📁 Output directory: {self.output_dir}")
            print("=" * 60)
            
            # Start with priority pages
            urls_to_crawl = [urljoin(self.base_url, path) for path in self.priority_pages]
            crawl_results = []
            
            # Add the main documentation page
            urls_to_crawl.insert(0, f"{self.base_url}/documentation/data-api")
            
            while urls_to_crawl:
                url = urls_to_crawl.pop(0)
                
                if url in self.crawled_urls or url in self.failed_urls:
                    continue
                
                result = await self.crawl_page(crawler, url)
                
                if result:
                    crawl_results.append(result)
                    
                    # Add discovered links to crawl queue (limit to avoid infinite crawling)
                    for link in result['links'][:15]:  # Limit to first 15 links per page
                        if (link not in self.crawled_urls and 
                            link not in self.failed_urls and 
                            link not in urls_to_crawl and
                            self.is_target_api_page(link)):
                            urls_to_crawl.append(link)
                
                # Be respectful with delays
                await asyncio.sleep(self.delay)
            
            # Save crawl summary
            summary = {
                'crawl_date': datetime.now().isoformat(),
                'target_sections': self.target_sections,
                'total_pages_crawled': len(crawl_results),
                'failed_urls': list(self.failed_urls),
                'crawled_pages': crawl_results
            }
            
            summary_file = os.path.join(self.output_dir, 'crawl_summary.json')
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2)
            
            # Create index file
            await self.create_index_file(crawl_results)
            
            print("\n" + "=" * 60)
            print(f"🎉 CoinDesk API Crawl Complete!")
            print(f"📊 Pages crawled: {len(crawl_results)}")
            print(f"❌ Failed URLs: {len(self.failed_urls)}")
            print(f"📁 Output directory: {self.output_dir}")
            
            if self.failed_urls:
                print(f"\n⚠️  Failed URLs:")
                for url in list(self.failed_urls)[:10]:  # Show first 10
                    print(f"   • {url}")
    
    async def create_index_file(self, crawl_results):
        """Create an index file with all crawled content"""
        index_file = os.path.join(self.output_dir, 'README.md')
        
        with open(index_file, 'w', encoding='utf-8') as f:
            f.write("# CoinDesk Data API Documentation - Complete Crawl\n\n")
            f.write(f"**Crawled on:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**Target sections:** {', '.join(self.target_sections)}\n")
            f.write(f"**Total pages:** {len(crawl_results)}\n\n")
            f.write("## 📋 Crawled API Documentation\n\n")
            
            # Group by API section
            indices_pages = [r for r in crawl_results if any(s in r['url'].lower() for s in ['indices', 'ref-rates', 'reference-rates'])]
            spot_pages = [r for r in crawl_results if 'spot' in r['url'].lower()]
            futures_pages = [r for r in crawl_results if 'futures' in r['url'].lower()]
            news_pages = [r for r in crawl_results if 'news' in r['url'].lower()]
            overview_pages = [r for r in crawl_results if 'overview' in r['url'].lower()]
            other_pages = [r for r in crawl_results if r not in indices_pages + spot_pages + futures_pages + news_pages + overview_pages]
            
            if indices_pages:
                f.write("### 📊 Indices & Reference Rates\n\n")
                for result in sorted(indices_pages, key=lambda x: x['url']):
                    f.write(f"- [{result['title']}]({result['filename']}) - {result['content_length']} chars\n")
                f.write("\n")
            
            if spot_pages:
                f.write("### 💰 Spot Data\n\n")
                for result in sorted(spot_pages, key=lambda x: x['url']):
                    f.write(f"- [{result['title']}]({result['filename']}) - {result['content_length']} chars\n")
                f.write("\n")
            
            if futures_pages:
                f.write("### 📈 Futures Data\n\n")
                for result in sorted(futures_pages, key=lambda x: x['url']):
                    f.write(f"- [{result['title']}]({result['filename']}) - {result['content_length']} chars\n")
                f.write("\n")
            
            if news_pages:
                f.write("### 📰 News API\n\n")
                for result in sorted(news_pages, key=lambda x: x['url']):
                    f.write(f"- [{result['title']}]({result['filename']}) - {result['content_length']} chars\n")
                f.write("\n")
            
            if overview_pages:
                f.write("### 🔍 Overview & General\n\n")
                for result in sorted(overview_pages, key=lambda x: x['url']):
                    f.write(f"- [{result['title']}]({result['filename']}) - {result['content_length']} chars\n")
                f.write("\n")
            
            if other_pages:
                f.write("### 📚 Other Documentation\n\n")
                for result in sorted(other_pages, key=lambda x: x['url']):
                    f.write(f"- [{result['title']}]({result['filename']}) - {result['content_length']} chars\n")

if __name__ == "__main__":
    crawler = CoinDeskAPICrawler()
    asyncio.run(crawler.crawl_all())
